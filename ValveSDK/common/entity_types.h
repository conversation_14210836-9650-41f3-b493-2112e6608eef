/***
*
*	Copyright (c) 1999, 2000, Valve LLC. All rights reserved.
*	
*	This product contains software technology licensed from Id 
*	Software, Inc. ("Id Technology").  Id Technology (c) 1996 Id Software, Inc. 
*	All Rights Reserved.
*
*   Use, distribution, and modification of this source code and/or resulting
*   object code is restricted to non-commercial enhancements to products from
*   Valve LLC.  All other use, distribution, or modification is prohibited
*   without written permission from Valve LLC.
*
****/
// entity_types.h
#if !defined( ENTITY_TYPESH )
#define ENTITY_TYPESH

#define ET_NORMAL		0
#define ET_PLAYER		1
#define ET_TEMPENTITY	2
#define ET_BEAM			3
// BMODEL or SPRITE that was split across BSP nodes
#define ET_FRAGMENTED	4

#endif // !ENTITY_TYPESH
