#include "drawing.h"
#include "main.h"

#pragma warning( disable:4996 )

void CDrawing::FillArea( int x, int y, int w, int h, int r, int g, int b, int a )
{
	g_Engine.pfnTintRGBA( x, y, w, h, r, g, b, a );
}

void CDrawing::DrawBox( int x, int y, int w, int h, int linewidth, int r, int g, int b, int a )
{
	FillArea( x,					y,					w,					linewidth,		r, g, b, a );
	FillArea( x + w - linewidth,	y + linewidth,		linewidth,			h - linewidth,	r, g, b, a );
	FillArea( x,					y + linewidth,		linewidth,			h - linewidth,	r, g, b, a );
	FillArea( x + linewidth,		y + h - linewidth,	w - linewidth * 2,	linewidth,		r, g, b, a );
}

int CDrawing::iStringLen( const char *fmt, ... )
{
	va_list va_alist;
	char buf[256];
	va_start( va_alist, fmt );
	_vsnprintf( buf, sizeof( buf ), fmt, va_alist );
	va_end( va_alist );
	int iWidth, iHeight;
	g_Engine.pfnDrawConsoleStringLen( buf, &iWidth, &iHeight );
	return iWidth;
}

int CDrawing::iStringHeight( void )
{
	int iWidth, iHeight;
	g_Engine.pfnDrawConsoleStringLen( "F", &iWidth, &iHeight );
	return iHeight;
}

void CDrawing::DrawString( int x, int y, int r, int g, int b, const char *fmt, ... )
{
	va_list va_alist;
	char buf[256];
	va_start( va_alist, fmt );
	_vsnprintf( buf, sizeof( buf ), fmt, va_alist );
	va_end( va_alist );
	g_Engine.pfnDrawSetTextColor( (float)r / 255.0f, (float)g / 255.0f, (float)b / 255.0f );
	g_Engine.pfnDrawConsoleString( x, y, buf );
}

void CDrawing::DrawStringCenter( int x, int y, int r, int g, int b, const char *fmt, ... )
{
	va_list va_alist;
	char buf[256];
	va_start( va_alist, fmt );
	_vsnprintf( buf, sizeof( buf ), fmt, va_alist );
	va_end( va_alist );
	int iWidth = iStringLen( "%s", buf );
	g_Engine.pfnDrawSetTextColor( (float)r / 255.0f, (float)g / 255.0f, (float)b / 255.0f );
	g_Engine.pfnDrawConsoleString( x - iWidth / 2, y, buf );
}

void CDrawing::DrawLine(int x1, int y1, int x2, int y2, int width, int r, int g, int b, int a)
{
	// Use the triangle API to draw a line
	g_Engine.pTriAPI->RenderMode(kRenderTransColor);
	g_Engine.pTriAPI->Color4f(r/255.0f, g/255.0f, b/255.0f, a/255.0f);

	g_Engine.pTriAPI->Begin(TRI_LINES);
	g_Engine.pTriAPI->Vertex3f(x1, y1, 0);
	g_Engine.pTriAPI->Vertex3f(x2, y2, 0);
	g_Engine.pTriAPI->End();
}

void CDrawing::DrawTriangle(Vector2D points[3], int r, int g, int b, int a)
{
    // Draw the three lines that make up the triangle outline
    DrawLine((int)points[0].x, (int)points[0].y, (int)points[1].x, (int)points[1].y, 1, r, g, b, a);
    DrawLine((int)points[1].x, (int)points[1].y, (int)points[2].x, (int)points[2].y, 1, r, g, b, a);
    DrawLine((int)points[2].x, (int)points[2].y, (int)points[0].x, (int)points[0].y, 1, r, g, b, a);
}

CDrawing g_Drawing;
