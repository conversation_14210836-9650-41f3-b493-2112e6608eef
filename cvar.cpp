#include "Main.h"
#include "gamemode.h"

CVARlist cvar;

#pragma warning(disable:4244)

// Forward declarations
void HlEngineCommand(const char* command);

// Variable to track if aimbot is activated via console command
bool g_bAimbotActivatedByCommand = false;

// Variable to track if perfect block is activated via console command
bool g_bPerfectBlockActivatedByCommand = false;

// Variable to manually force free-for-all mode for testing
bool g_bForceFreeForAll = false;

// Current detected game mode
GameMode g_GameMode = GAMEMODE_UNKNOWN;

// Function to detect game mode based on player teams
void DetectGameMode() {
    // If manually forced, use that
    if (g_bForceFreeForAll) {
        g_GameMode = GAMEMODE_FREEFORALL;
        return;
    }

    // Check server cvars first
    cvar_t* mp_gamemode = g_Engine.pfnGetCvarPointer("mp_gamemode");
    if (mp_gamemode && mp_gamemode->value == 0) {
        g_GameMode = GAMEMODE_FREEFORALL;
        return;
    }

    cvar_t* sv_gamemode = g_Engine.pfnGetCvarPointer("sv_gamemode");
    if (sv_gamemode && sv_gamemode->value == 0) {
        g_GameMode = GAMEMODE_FREEFORALL;
        return;
    }

    cvar_t* mp_teamplay = g_Engine.pfnGetCvarPointer("mp_teamplay");
    if (mp_teamplay && mp_teamplay->value == 0) {
        g_GameMode = GAMEMODE_FREEFORALL;
        return;
    }

    // If we have at least 3 players, check if they all have different teams
    // This is a heuristic for detecting free-for-all mode
    int playerCount = 0;
    int teamCounts[4] = {0}; // Count for each team (GOOD, EVIL, SPEC, NONE)

    for (int i = 1; i < 33; i++) {
        if (g_Player[i].bAlive) {
            playerCount++;

            // Get player's team
            int team = g_Player[i].iTeam;
            if (team >= 0 && team < 4) {
                teamCounts[team]++;
            }
        }
    }

    // If we have at least 3 players and they're all on different teams or all on TEAM_NONE
    if (playerCount >= 3) {
        if (teamCounts[3] == playerCount) { // All players on TEAM_NONE
            g_GameMode = GAMEMODE_FREEFORALL;
            return;
        }

        // If we have players on both TEAM_GOOD and TEAM_EVIL, it's team play
        if (teamCounts[0] > 0 && teamCounts[1] > 0) {
            g_GameMode = GAMEMODE_TEAMPLAY;
            return;
        }
    }

    // Default to team play if we couldn't determine
    g_GameMode = GAMEMODE_TEAMPLAY;
}

// Function to check if we're in free-for-all mode
bool IsFreeForAllMode() {
    // If game mode is unknown, detect it first
    if (g_GameMode == GAMEMODE_UNKNOWN) {
        DetectGameMode();
    }

    return (g_GameMode == GAMEMODE_FREEFORALL);
}

void func_aim_plus()
{
	// Activate aimbot
	g_bAimbotActivatedByCommand = true;
	cvar.aim_active = 1;
	g_Engine.Con_Printf("Aimbot activated\n");
}

void func_aim_minus()
{
	// Deactivate aimbot
	g_bAimbotActivatedByCommand = false;
	cvar.aim_active = 0;
	g_Engine.Con_Printf("Aimbot deactivated\n");
}

void func_pblock_plus()
{
	// Activate perfect block
	g_bPerfectBlockActivatedByCommand = true;
	g_Engine.Con_Printf("Perfect Block activated\n");
}

void func_pblock_minus()
{
	// Deactivate perfect block
	g_bPerfectBlockActivatedByCommand = false;
	HlEngineCommand("-block");
	HlEngineCommand("-aim");
	g_Engine.Con_Printf("Perfect Block deactivated\n");
}

// Function to toggle free-for-all mode manually for testing
void func_toggle_ffa()
{
	g_bForceFreeForAll = !g_bForceFreeForAll;

	// Update game mode immediately
	if (g_bForceFreeForAll) {
		g_GameMode = GAMEMODE_FREEFORALL;
		g_Engine.Con_Printf("Free-for-all mode FORCED ON (All players will be treated as enemies)\n");
	} else {
		// Re-detect game mode
		DetectGameMode();
		g_Engine.Con_Printf("Free-for-all mode DISABLED (Auto-detection: %s)\n",
			g_GameMode == GAMEMODE_FREEFORALL ? "Free-for-all" : "Team play");
	}
}


void func_alias()
{
	const char* name = cmd.argC(1);
	string& content = cmd.argS(2);
	cmd.AddAlias(name,content);
}

// Removed teleswoop function

void set_cvar()
{
	char set_cvars[56];
	if ( cmd.names.find(g_Engine.Cmd_Argv(1)) )
	{
		sprintf(set_cvars,"%s %s",g_Engine.Cmd_Argv(1),g_Engine.Cmd_Argv(2));
		cmd.exec(set_cvars);
	}
	else
		cmd.exec(g_Engine.Cmd_Argv(1));
}

void CVARlist::init()
{

	memset((char*)this, 0, sizeof(*this));
	#define REGISTER_CVAR_FLOAT(name,defaultvalue) cmd.AddCvarFloat(#name, &##name );name=defaultvalue##f;
	#define REGISTER_CVAR_INT(name,defaultvalue) cmd.AddCvarInt(#name, &##name );name=defaultvalue;
	#define REGISTER_CVAR_STR(name,defaultvalue) cmd.AddCvarString(#name, &##name );name=defaultvalue;
	#define REGISTER_COMMAND(name) cmd.AddCommand( #name, func_##name);

	//Otomatik ayarlar

	REGISTER_CVAR_INT(aim_active, 1)
	REGISTER_CVAR_INT(aim_target, 10)
	REGISTER_CVAR_FLOAT(aim_height, 0.20)
	REGISTER_CVAR_INT(aim_autowall, 0)
	REGISTER_CVAR_INT(aim_fov, 35)
	REGISTER_CVAR_INT(aim_distancebasedfov, 1)
	REGISTER_CVAR_INT(aim_avdraw, 1)
	REGISTER_CVAR_INT(aim_prediction, 1)
	REGISTER_CVAR_INT(aim_time, 300)
	REGISTER_CVAR_INT(aim_delay, 0)
	REGISTER_CVAR_INT(aim_smoothness, 1)
	REGISTER_CVAR_INT(bhop, 1)
	REGISTER_CVAR_INT(strafe_hack, 0)
	REGISTER_CVAR_INT(aim_triggerbot, 1)
	REGISTER_CVAR_INT(aim_triggerbot_fov, 11)

	REGISTER_COMMAND(alias)

	// Register +aim and -aim commands for activating/deactivating aimbot
	g_Engine.pfnAddCommand("+aim", func_aim_plus);
	g_Engine.pfnAddCommand("-aim", func_aim_minus);

	// Register +pblock and -pblock commands for activating/deactivating perfect block
	g_Engine.pfnAddCommand("+pblock", func_pblock_plus);
	g_Engine.pfnAddCommand("-pblock", func_pblock_minus);

	// Register toggle_ffa command for testing free-for-all detection
	g_Engine.pfnAddCommand("toggle_ffa", func_toggle_ffa);

	g_Engine.pfnAddCommand("set",set_cvar);
}

void HlEngineCommand(const char* command)
{
	if(!g_Engine.pfnClientCmd) { return; }
	g_Engine.pfnClientCmd( const_cast<char*>(command) );
}

void HandleCvarInt(char* name, int* value)
{
	char* arg1 = cmd.argC(1);
	if (!strcmp(arg1,"change"))
	{
		if(*value) *value=0;
		else *value=1;
		return;
	}
	if (!strcmp(arg1,"up")){*value += cmd.argI(2);return;}
	if (!strcmp(arg1,"down")){*value -= cmd.argI(2);return;}
	if (!strcmp(arg1,"hide")){*value = cmd.argI(2);return;}
	if (!*arg1)
	{
		g_Engine.Con_Printf( "CVAR %s = %i\n",name,*value);
		return;
	}
	*value = cmd.argI(1);
}

void HandleCvarStr(char* name, string value)
{
	char* arg1 = cmd.argC(1);
	if (!*arg1)
	{
		g_Engine.Con_Printf("CVAR %s = %s\n", name, value.c_str());
		return;
	}
	value = cmd.argF(1);
}

void HandleCvarFloat(char* name, float* value)
{
	char* arg1 = cmd.argC(1);
	if (!strcmp(arg1,"change"))
	{
		if(*value) *value=0;
		else *value=1;
		return;
	}
	if (!strcmp(arg1,"up")){*value += cmd.argF(2);return;}
	if (!strcmp(arg1,"down")){*value -= cmd.argF(2);return;}
	if (!strcmp(arg1,"hide")){*value = cmd.argI(2);return;}
	if (!*arg1)
	{
		g_Engine.Con_Printf("CVAR %s = %f\n",name,*value);
		return;
	}
	*value = cmd.argF(1);
}

bool isHlCvar(char* name)
{
	if(!g_Engine.pfnGetCvarPointer) { return false; }
	cvar_s* test = g_Engine.pfnGetCvarPointer(name);
	return (test!=NULL);
}

bool HandleHlCvar(char* name)
{
	if(!g_Engine.pfnGetCvarPointer) { return false; }
	cvar_s* ptr = g_Engine.pfnGetCvarPointer(name);
	if(!ptr) { return false; }
	HandleCvarFloat(name,&ptr->value);
	return true;
}
