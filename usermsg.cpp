#include "main.h"

pfnUserMsgHook pTeamInfo = NULL;

int TeamInfo( const char *pszName, int iSize, void *pbuf )
{
	//add_log("pTeamInfo");
	BEGIN_READ( pbuf, iSize );
	int iIndex = READ_BYTE();
	char *szTeam = READ_STRING();

	// Log the original team name for debugging
	g_Engine.Con_Printf("TeamInfo: Player %d original team name: '%s'\n", iIndex, szTeam);

	_strlwr( szTeam );

	// External variable to force free-for-all mode
	extern bool g_bForceFreeForAll;

	// Check for free-for-all mode using multiple possible cvars
	bool isFreeForAll = g_bForceFreeForAll; // First check if manually forced

	if (!isFreeForAll) {
		// Check mp_gamemode (primary cvar)
		cvar_t* mp_gamemode = g_Engine.pfnGetCvarPointer("mp_gamemode");
		if (mp_gamemode && mp_gamemode->value == 0) {
			isFreeForAll = true;
		}

		// Check sv_gamemode (old incorrect cvar, but might be used by some servers)
		if (!isFreeForAll) {
			cvar_t* sv_gamemode = g_Engine.pfnGetCvarPointer("sv_gamemode");
			if (sv_gamemode && sv_gamemode->value == 0) {
				isFreeForAll = true;
			}
		}

		// Check mp_teamplay (some mods use this)
		if (!isFreeForAll) {
			cvar_t* mp_teamplay = g_Engine.pfnGetCvarPointer("mp_teamplay");
			if (mp_teamplay && mp_teamplay->value == 0) {
				isFreeForAll = true;
			}
		}
	}

	// Log the free-for-all detection result in TeamInfo
	g_Engine.Con_Printf("TeamInfo: Free-for-all detection: %s\n", isFreeForAll ? "TRUE" : "FALSE");

	if (isFreeForAll)
	{
		// In free-for-all mode, everyone is on their own team
		if( iIndex == g_Local.iIndex ) { g_Local.iTeam = 0; }
		g_Player[iIndex].iTeam = 0;
	}
	else
	{
		// Team-based mode - more flexible matching for team names
		if( !strcmp( szTeam, "good" ) || strstr( szTeam, "good" ) || !strcmp( szTeam, "blue" ) )
		{
			if( iIndex == g_Local.iIndex ) { g_Local.iTeam = 1; }
			g_Player[iIndex].iTeam = 1;
			g_Engine.Con_Printf("Player %d assigned to GOOD team\n", iIndex);
		}
		else if( !strcmp( szTeam, "evil" ) || strstr( szTeam, "evil" ) || !strcmp( szTeam, "red" ) )
		{
			if( iIndex == g_Local.iIndex ) { g_Local.iTeam = 2; }
			g_Player[iIndex].iTeam = 2;
			g_Engine.Con_Printf("Player %d assigned to EVIL team\n", iIndex);
		}
		else
		{
			if( iIndex == g_Local.iIndex ) { g_Local.iTeam = 0; }
			g_Player[iIndex].iTeam = 0;
			g_Engine.Con_Printf("Player %d assigned to NONE team (team string: %s)\n", iIndex, szTeam);
		}
	}

	return (*pTeamInfo)( pszName, iSize, pbuf );
}

// Removed CurWeapon function

int pfnHookUserMsg( char *szMsgName, pfnUserMsgHook pfn )
{
	#define HOOK_MSG(name) \
	if( !strcmp( szMsgName, #name ) ) \
	{ \
		p##name = pfn; \
		return g_Engine.pfnHookUserMsg( szMsgName, ##name ); \
	}

	HOOK_MSG(TeamInfo)

	return g_Engine.pfnHookUserMsg( szMsgName, pfn );
}