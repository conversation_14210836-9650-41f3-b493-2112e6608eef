﻿<?xml version="1.0" encoding="utf-8"?>
<Project DefaultTargets="Build" ToolsVersion="14.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <ItemGroup Label="ProjectConfigurations">
    <ProjectConfiguration Include="Debug|Win32">
      <Configuration>Debug</Configuration>
      <Platform>Win32</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="Release|Win32">
      <Configuration>Release</Configuration>
      <Platform>Win32</Platform>
    </ProjectConfiguration>
  </ItemGroup>
  <PropertyGroup Label="Globals">
    <ProjectGuid>{B5E2AC2B-6700-4B21-80DD-C722E828B2FE}</ProjectGuid>
    <Keyword>Win32Proj</Keyword>
    <RootNamespace>RobinBot</RootNamespace>
    <ProjectName>RobinBot</ProjectName>
  </PropertyGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.Default.props" />
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Debug|Win32'" Label="Configuration">
    <ConfigurationType>DynamicLibrary</ConfigurationType>
    <UseDebugLibraries>true</UseDebugLibraries>
    <PlatformToolset>v143</PlatformToolset>
    <CharacterSet>Unicode</CharacterSet>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Release|Win32'" Label="Configuration">
    <ConfigurationType>DynamicLibrary</ConfigurationType>
    <UseDebugLibraries>false</UseDebugLibraries>
    <WholeProgramOptimization>true</WholeProgramOptimization>
    <CharacterSet>Unicode</CharacterSet>
    <PlatformToolset>v143</PlatformToolset>
    <CLRSupport>false</CLRSupport>
  </PropertyGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.props" />
  <ImportGroup Label="ExtensionSettings">
  </ImportGroup>
  <ImportGroup Label="PropertySheets" Condition="'$(Configuration)|$(Platform)'=='Debug|Win32'">
    <Import Project="$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props" Condition="exists('$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props')" Label="LocalAppDataPlatform" />
  </ImportGroup>
  <ImportGroup Label="PropertySheets" Condition="'$(Configuration)|$(Platform)'=='Release|Win32'">
    <Import Project="$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props" Condition="exists('$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props')" Label="LocalAppDataPlatform" />
  </ImportGroup>
  <PropertyGroup Label="UserMacros" />
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Debug|Win32'">
    <LinkIncremental>true</LinkIncremental>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Release|Win32'">
    <LinkIncremental>false</LinkIncremental>
    <GenerateManifest>false</GenerateManifest>
  </PropertyGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Debug|Win32'">
    <ClCompile>
      <PrecompiledHeader>
      </PrecompiledHeader>
      <WarningLevel>Level3</WarningLevel>
      <Optimization>Disabled</Optimization>
      <PreprocessorDefinitions>WIN32;_DEBUG;_WINDOWS;_USRDLL;ENGINEHACKBASENEW_EXPORTS;%(PreprocessorDefinitions)</PreprocessorDefinitions>
    </ClCompile>
    <Link>
      <SubSystem>Windows</SubSystem>
      <GenerateDebugInformation>true</GenerateDebugInformation>
    </Link>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Release|Win32'">
    <ClCompile>
      <WarningLevel>Level3</WarningLevel>
      <PrecompiledHeader>
      </PrecompiledHeader>
      <Optimization>MinSpace</Optimization>
      <FunctionLevelLinking>true</FunctionLevelLinking>
      <PreprocessorDefinitions>WIN32;NDEBUG;_WINDOWS;%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <FavorSizeOrSpeed>Size</FavorSizeOrSpeed>
      <BufferSecurityCheck>false</BufferSecurityCheck>
      <BrowseInformation>false</BrowseInformation>
      <EnableEnhancedInstructionSet>NoExtensions</EnableEnhancedInstructionSet>
      <RuntimeLibrary>MultiThreaded</RuntimeLibrary>
      <FloatingPointModel>Fast</FloatingPointModel>
    </ClCompile>
    <Link>
      <SubSystem>Windows</SubSystem>
      <GenerateDebugInformation>false</GenerateDebugInformation>
      <EnableCOMDATFolding>true</EnableCOMDATFolding>
      <OptimizeReferences>true</OptimizeReferences>
      <FixedBaseAddress>false</FixedBaseAddress>
      <EnableUAC>false</EnableUAC>
      <ImageHasSafeExceptionHandlers>true</ImageHasSafeExceptionHandlers>
      <AdditionalDependencies>%(AdditionalDependencies)</AdditionalDependencies>
    </Link>
    <Bscmake>
      <PreserveSbr>false</PreserveSbr>
    </Bscmake>
  </ItemDefinitionGroup>
  <ItemGroup>
    <ClInclude Include="AutoOffset.h" />
    <ClInclude Include="Client.h" />
    <ClInclude Include="Command.h" />
    <ClInclude Include="cvar.h" />
    <ClInclude Include="detours.h" />
    <ClInclude Include="drawing.h" />
    <ClInclude Include="interpreter.h" />
    <ClInclude Include="Main.h" />
    <ClInclude Include="ModuleSecurity.h" />
    <ClInclude Include="players.h" />
    <ClInclude Include="SetupHooks.h" />
    <ClInclude Include="stringfinder.h" />
    <ClInclude Include="SvcMessage.h" />
    <ClInclude Include="ValveSDK\common\beamdef.h" />
    <ClInclude Include="ValveSDK\common\cl_entity.h" />
    <ClInclude Include="ValveSDK\common\com_model.h" />
    <ClInclude Include="ValveSDK\common\const.h" />
    <ClInclude Include="ValveSDK\common\con_nprint.h" />
    <ClInclude Include="ValveSDK\common\crc.h" />
    <ClInclude Include="ValveSDK\common\cvardef.h" />
    <ClInclude Include="ValveSDK\common\demo_api.h" />
    <ClInclude Include="ValveSDK\common\director_cmds.h" />
    <ClInclude Include="ValveSDK\common\dlight.h" />
    <ClInclude Include="ValveSDK\common\dll_state.h" />
    <ClInclude Include="ValveSDK\common\engine_launcher_api.h" />
    <ClInclude Include="ValveSDK\common\entity_state.h" />
    <ClInclude Include="ValveSDK\common\entity_types.h" />
    <ClInclude Include="ValveSDK\common\event_api.h" />
    <ClInclude Include="ValveSDK\common\event_args.h" />
    <ClInclude Include="ValveSDK\common\event_flags.h" />
    <ClInclude Include="ValveSDK\common\exefuncs.h" />
    <ClInclude Include="ValveSDK\common\hltv.h" />
    <ClInclude Include="ValveSDK\common\interface.h" />
    <ClInclude Include="ValveSDK\common\in_buttons.h" />
    <ClInclude Include="ValveSDK\common\itrackeruser.h" />
    <ClInclude Include="ValveSDK\common\ivoicetweak.h" />
    <ClInclude Include="ValveSDK\common\mathlib.h" />
    <ClInclude Include="ValveSDK\common\netadr.h" />
    <ClInclude Include="ValveSDK\common\net_api.h" />
    <ClInclude Include="ValveSDK\common\nowin.h" />
    <ClInclude Include="ValveSDK\common\particledef.h" />
    <ClInclude Include="ValveSDK\common\pmtrace.h" />
    <ClInclude Include="ValveSDK\common\qfont.h" />
    <ClInclude Include="ValveSDK\common\ref_params.h" />
    <ClInclude Include="ValveSDK\common\r_efx.h" />
    <ClInclude Include="ValveSDK\common\screenfade.h" />
    <ClInclude Include="ValveSDK\common\studio_event.h" />
    <ClInclude Include="ValveSDK\common\triangleapi.h" />
    <ClInclude Include="ValveSDK\common\usercmd.h" />
    <ClInclude Include="ValveSDK\common\weaponinfo.h" />
    <ClInclude Include="ValveSDK\engine\anorms.h" />
    <ClInclude Include="ValveSDK\engine\beamdef.h" />
    <ClInclude Include="ValveSDK\engine\cdll_dll.h" />
    <ClInclude Include="ValveSDK\engine\cdll_engine_dll.h" />
    <ClInclude Include="ValveSDK\engine\Cdll_int.h" />
    <ClInclude Include="ValveSDK\engine\cl_dll.h" />
    <ClInclude Include="ValveSDK\engine\cl_entity.h" />
    <ClInclude Include="ValveSDK\engine\const.h" />
    <ClInclude Include="ValveSDK\engine\crc.h" />
    <ClInclude Include="ValveSDK\engine\custom.h" />
    <ClInclude Include="ValveSDK\engine\customentity.h" />
    <ClInclude Include="ValveSDK\engine\cvardef.h" />
    <ClInclude Include="ValveSDK\engine\dlight.h" />
    <ClInclude Include="ValveSDK\engine\edict.h" />
    <ClInclude Include="ValveSDK\engine\eiface.h" />
    <ClInclude Include="ValveSDK\engine\entity_state.h" />
    <ClInclude Include="ValveSDK\engine\entity_types.h" />
    <ClInclude Include="ValveSDK\engine\event_args.h" />
    <ClInclude Include="ValveSDK\engine\event_flags.h" />
    <ClInclude Include="ValveSDK\engine\in_buttons.h" />
    <ClInclude Include="ValveSDK\engine\keydefs.h" />
    <ClInclude Include="ValveSDK\engine\particledef.h" />
    <ClInclude Include="ValveSDK\engine\pmtrace.h" />
    <ClInclude Include="ValveSDK\engine\pm_defs.h" />
    <ClInclude Include="ValveSDK\engine\pm_info.h" />
    <ClInclude Include="ValveSDK\engine\progdefs.h" />
    <ClInclude Include="ValveSDK\engine\progs.h" />
    <ClInclude Include="ValveSDK\engine\r_efx.h" />
    <ClInclude Include="ValveSDK\engine\r_studioint.h" />
    <ClInclude Include="ValveSDK\engine\shake.h" />
    <ClInclude Include="ValveSDK\engine\studio.h" />
    <ClInclude Include="ValveSDK\engine\studio_event.h" />
    <ClInclude Include="ValveSDK\engine\triangleapi.h" />
    <ClInclude Include="ValveSDK\engine\usercmd.h" />
    <ClInclude Include="ValveSDK\engine\util_vector.h" />
    <ClInclude Include="ValveSDK\engine\weaponinfo.h" />
    <ClInclude Include="ValveSDK\engine\wrect.h" />
    <ClInclude Include="ValveSDK\misc\com_model.h" />
    <ClInclude Include="ValveSDK\misc\parsemsg.h" />
  </ItemGroup>
  <ItemGroup>
    <ClCompile Include="AutoOffset.cpp" />
    <ClCompile Include="Client.cpp" />
    <ClCompile Include="Command.cpp" />
    <ClCompile Include="cvar.cpp" />
    <ClCompile Include="drawing.cpp" />
    <ClCompile Include="interpreter.cpp" />
    <ClCompile Include="Main.cpp" />
    <ClCompile Include="ModuleSecurity.cpp" />
    <ClCompile Include="players.cpp" />
    <ClCompile Include="SetupHooks.cpp" />
    <ClCompile Include="stringfinder.cpp" />
    <ClCompile Include="SvcMessage.cpp" />
    <ClCompile Include="usermsg.cpp" />
    <ClCompile Include="ValveSDK\common\interface.cpp" />
    <ClCompile Include="ValveSDK\misc\parsemsg.cpp" />
  </ItemGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.targets" />
  <ImportGroup Label="ExtensionTargets">
  </ImportGroup>
</Project>