﻿<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="4.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <ItemGroup>
    <Filter Include="Header Files">
      <UniqueIdentifier>{93995380-89BD-4b04-88EB-625FBE52EBFB}</UniqueIdentifier>
      <Extensions>h;hpp;hxx;hm;inl;inc;xsd</Extensions>
    </Filter>
    <Filter Include="Header Files\ValveSDK">
      <UniqueIdentifier>{e30efe6f-66de-4955-8dc9-bd37109937f1}</UniqueIdentifier>
    </Filter>
    <Filter Include="Header Files\ValveSDK\common">
      <UniqueIdentifier>{5df9d895-9c1f-40b0-9b68-893412bdd4b7}</UniqueIdentifier>
    </Filter>
    <Filter Include="Header Files\ValveSDK\engine">
      <UniqueIdentifier>{b673195a-7fc3-41b9-817f-ab9db354a5b0}</UniqueIdentifier>
    </Filter>
    <Filter Include="Header Files\ValveSDK\misc">
      <UniqueIdentifier>{6a0c7a00-4513-4bb0-8ae1-350e2ddfc6df}</UniqueIdentifier>
    </Filter>
    <Filter Include="Sources">
      <UniqueIdentifier>{4FC737F1-C7A5-4376-A066-2A32D752A2FF}</UniqueIdentifier>
      <Extensions>cpp;c;cc;cxx;def;odl;idl;hpj;bat;asm;asmx</Extensions>
    </Filter>
    <Filter Include="Resources">
      <UniqueIdentifier>{67DA6AB6-F800-4c08-8B7A-83BB121AAD01}</UniqueIdentifier>
      <Extensions>rc;ico;cur;bmp;dlg;rc2;rct;bin;rgs;gif;jpg;jpeg;jpe;resx;tiff;tif;png;wav;mfcribbon-ms</Extensions>
    </Filter>
  </ItemGroup>
  <ItemGroup>
    <ClInclude Include="AutoOffset.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="Main.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="ValveSDK\common\beamdef.h">
      <Filter>Header Files\ValveSDK\common</Filter>
    </ClInclude>
    <ClInclude Include="ValveSDK\common\cl_entity.h">
      <Filter>Header Files\ValveSDK\common</Filter>
    </ClInclude>
    <ClInclude Include="ValveSDK\common\com_model.h">
      <Filter>Header Files\ValveSDK\common</Filter>
    </ClInclude>
    <ClInclude Include="ValveSDK\common\con_nprint.h">
      <Filter>Header Files\ValveSDK\common</Filter>
    </ClInclude>
    <ClInclude Include="ValveSDK\common\const.h">
      <Filter>Header Files\ValveSDK\common</Filter>
    </ClInclude>
    <ClInclude Include="ValveSDK\common\crc.h">
      <Filter>Header Files\ValveSDK\common</Filter>
    </ClInclude>
    <ClInclude Include="ValveSDK\common\cvardef.h">
      <Filter>Header Files\ValveSDK\common</Filter>
    </ClInclude>
    <ClInclude Include="ValveSDK\common\demo_api.h">
      <Filter>Header Files\ValveSDK\common</Filter>
    </ClInclude>
    <ClInclude Include="ValveSDK\common\director_cmds.h">
      <Filter>Header Files\ValveSDK\common</Filter>
    </ClInclude>
    <ClInclude Include="ValveSDK\common\dlight.h">
      <Filter>Header Files\ValveSDK\common</Filter>
    </ClInclude>
    <ClInclude Include="ValveSDK\common\dll_state.h">
      <Filter>Header Files\ValveSDK\common</Filter>
    </ClInclude>
    <ClInclude Include="ValveSDK\common\engine_launcher_api.h">
      <Filter>Header Files\ValveSDK\common</Filter>
    </ClInclude>
    <ClInclude Include="ValveSDK\common\entity_state.h">
      <Filter>Header Files\ValveSDK\common</Filter>
    </ClInclude>
    <ClInclude Include="ValveSDK\common\entity_types.h">
      <Filter>Header Files\ValveSDK\common</Filter>
    </ClInclude>
    <ClInclude Include="ValveSDK\common\event_api.h">
      <Filter>Header Files\ValveSDK\common</Filter>
    </ClInclude>
    <ClInclude Include="ValveSDK\common\event_args.h">
      <Filter>Header Files\ValveSDK\common</Filter>
    </ClInclude>
    <ClInclude Include="ValveSDK\common\event_flags.h">
      <Filter>Header Files\ValveSDK\common</Filter>
    </ClInclude>
    <ClInclude Include="ValveSDK\common\exefuncs.h">
      <Filter>Header Files\ValveSDK\common</Filter>
    </ClInclude>
    <ClInclude Include="ValveSDK\common\hltv.h">
      <Filter>Header Files\ValveSDK\common</Filter>
    </ClInclude>
    <ClInclude Include="ValveSDK\common\in_buttons.h">
      <Filter>Header Files\ValveSDK\common</Filter>
    </ClInclude>
    <ClInclude Include="ValveSDK\common\interface.h">
      <Filter>Header Files\ValveSDK\common</Filter>
    </ClInclude>
    <ClInclude Include="ValveSDK\common\itrackeruser.h">
      <Filter>Header Files\ValveSDK\common</Filter>
    </ClInclude>
    <ClInclude Include="ValveSDK\common\ivoicetweak.h">
      <Filter>Header Files\ValveSDK\common</Filter>
    </ClInclude>
    <ClInclude Include="ValveSDK\common\mathlib.h">
      <Filter>Header Files\ValveSDK\common</Filter>
    </ClInclude>
    <ClInclude Include="ValveSDK\common\net_api.h">
      <Filter>Header Files\ValveSDK\common</Filter>
    </ClInclude>
    <ClInclude Include="ValveSDK\common\netadr.h">
      <Filter>Header Files\ValveSDK\common</Filter>
    </ClInclude>
    <ClInclude Include="ValveSDK\common\nowin.h">
      <Filter>Header Files\ValveSDK\common</Filter>
    </ClInclude>
    <ClInclude Include="ValveSDK\common\particledef.h">
      <Filter>Header Files\ValveSDK\common</Filter>
    </ClInclude>
    <ClInclude Include="ValveSDK\common\pmtrace.h">
      <Filter>Header Files\ValveSDK\common</Filter>
    </ClInclude>
    <ClInclude Include="ValveSDK\common\qfont.h">
      <Filter>Header Files\ValveSDK\common</Filter>
    </ClInclude>
    <ClInclude Include="ValveSDK\common\r_efx.h">
      <Filter>Header Files\ValveSDK\common</Filter>
    </ClInclude>
    <ClInclude Include="ValveSDK\common\ref_params.h">
      <Filter>Header Files\ValveSDK\common</Filter>
    </ClInclude>
    <ClInclude Include="ValveSDK\common\screenfade.h">
      <Filter>Header Files\ValveSDK\common</Filter>
    </ClInclude>
    <ClInclude Include="ValveSDK\common\studio_event.h">
      <Filter>Header Files\ValveSDK\common</Filter>
    </ClInclude>
    <ClInclude Include="ValveSDK\common\triangleapi.h">
      <Filter>Header Files\ValveSDK\common</Filter>
    </ClInclude>
    <ClInclude Include="ValveSDK\common\usercmd.h">
      <Filter>Header Files\ValveSDK\common</Filter>
    </ClInclude>
    <ClInclude Include="ValveSDK\common\weaponinfo.h">
      <Filter>Header Files\ValveSDK\common</Filter>
    </ClInclude>
    <ClInclude Include="ValveSDK\engine\anorms.h">
      <Filter>Header Files\ValveSDK\engine</Filter>
    </ClInclude>
    <ClInclude Include="ValveSDK\engine\beamdef.h">
      <Filter>Header Files\ValveSDK\engine</Filter>
    </ClInclude>
    <ClInclude Include="ValveSDK\engine\cdll_dll.h">
      <Filter>Header Files\ValveSDK\engine</Filter>
    </ClInclude>
    <ClInclude Include="ValveSDK\engine\cdll_engine_dll.h">
      <Filter>Header Files\ValveSDK\engine</Filter>
    </ClInclude>
    <ClInclude Include="ValveSDK\engine\Cdll_int.h">
      <Filter>Header Files\ValveSDK\engine</Filter>
    </ClInclude>
    <ClInclude Include="ValveSDK\engine\cl_dll.h">
      <Filter>Header Files\ValveSDK\engine</Filter>
    </ClInclude>
    <ClInclude Include="ValveSDK\engine\cl_entity.h">
      <Filter>Header Files\ValveSDK\engine</Filter>
    </ClInclude>
    <ClInclude Include="ValveSDK\engine\const.h">
      <Filter>Header Files\ValveSDK\engine</Filter>
    </ClInclude>
    <ClInclude Include="ValveSDK\engine\crc.h">
      <Filter>Header Files\ValveSDK\engine</Filter>
    </ClInclude>
    <ClInclude Include="ValveSDK\engine\custom.h">
      <Filter>Header Files\ValveSDK\engine</Filter>
    </ClInclude>
    <ClInclude Include="ValveSDK\engine\customentity.h">
      <Filter>Header Files\ValveSDK\engine</Filter>
    </ClInclude>
    <ClInclude Include="ValveSDK\engine\cvardef.h">
      <Filter>Header Files\ValveSDK\engine</Filter>
    </ClInclude>
    <ClInclude Include="ValveSDK\engine\dlight.h">
      <Filter>Header Files\ValveSDK\engine</Filter>
    </ClInclude>
    <ClInclude Include="ValveSDK\engine\edict.h">
      <Filter>Header Files\ValveSDK\engine</Filter>
    </ClInclude>
    <ClInclude Include="ValveSDK\engine\eiface.h">
      <Filter>Header Files\ValveSDK\engine</Filter>
    </ClInclude>
    <ClInclude Include="ValveSDK\engine\entity_state.h">
      <Filter>Header Files\ValveSDK\engine</Filter>
    </ClInclude>
    <ClInclude Include="ValveSDK\engine\entity_types.h">
      <Filter>Header Files\ValveSDK\engine</Filter>
    </ClInclude>
    <ClInclude Include="ValveSDK\engine\event_args.h">
      <Filter>Header Files\ValveSDK\engine</Filter>
    </ClInclude>
    <ClInclude Include="ValveSDK\engine\event_flags.h">
      <Filter>Header Files\ValveSDK\engine</Filter>
    </ClInclude>
    <ClInclude Include="ValveSDK\engine\in_buttons.h">
      <Filter>Header Files\ValveSDK\engine</Filter>
    </ClInclude>
    <ClInclude Include="ValveSDK\engine\keydefs.h">
      <Filter>Header Files\ValveSDK\engine</Filter>
    </ClInclude>
    <ClInclude Include="ValveSDK\engine\particledef.h">
      <Filter>Header Files\ValveSDK\engine</Filter>
    </ClInclude>
    <ClInclude Include="ValveSDK\engine\pm_defs.h">
      <Filter>Header Files\ValveSDK\engine</Filter>
    </ClInclude>
    <ClInclude Include="ValveSDK\engine\pm_info.h">
      <Filter>Header Files\ValveSDK\engine</Filter>
    </ClInclude>
    <ClInclude Include="ValveSDK\engine\pmtrace.h">
      <Filter>Header Files\ValveSDK\engine</Filter>
    </ClInclude>
    <ClInclude Include="ValveSDK\engine\progdefs.h">
      <Filter>Header Files\ValveSDK\engine</Filter>
    </ClInclude>
    <ClInclude Include="ValveSDK\engine\progs.h">
      <Filter>Header Files\ValveSDK\engine</Filter>
    </ClInclude>
    <ClInclude Include="ValveSDK\engine\r_efx.h">
      <Filter>Header Files\ValveSDK\engine</Filter>
    </ClInclude>
    <ClInclude Include="ValveSDK\engine\r_studioint.h">
      <Filter>Header Files\ValveSDK\engine</Filter>
    </ClInclude>
    <ClInclude Include="ValveSDK\engine\shake.h">
      <Filter>Header Files\ValveSDK\engine</Filter>
    </ClInclude>
    <ClInclude Include="ValveSDK\engine\studio.h">
      <Filter>Header Files\ValveSDK\engine</Filter>
    </ClInclude>
    <ClInclude Include="ValveSDK\engine\studio_event.h">
      <Filter>Header Files\ValveSDK\engine</Filter>
    </ClInclude>
    <ClInclude Include="ValveSDK\engine\triangleapi.h">
      <Filter>Header Files\ValveSDK\engine</Filter>
    </ClInclude>
    <ClInclude Include="ValveSDK\engine\usercmd.h">
      <Filter>Header Files\ValveSDK\engine</Filter>
    </ClInclude>
    <ClInclude Include="ValveSDK\engine\util_vector.h">
      <Filter>Header Files\ValveSDK\engine</Filter>
    </ClInclude>
    <ClInclude Include="ValveSDK\engine\weaponinfo.h">
      <Filter>Header Files\ValveSDK\engine</Filter>
    </ClInclude>
    <ClInclude Include="ValveSDK\engine\wrect.h">
      <Filter>Header Files\ValveSDK\engine</Filter>
    </ClInclude>
    <ClInclude Include="ValveSDK\misc\com_model.h">
      <Filter>Header Files\ValveSDK\misc</Filter>
    </ClInclude>
    <ClInclude Include="ValveSDK\misc\parsemsg.h">
      <Filter>Header Files\ValveSDK\misc</Filter>
    </ClInclude>
    <ClInclude Include="SetupHooks.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="Client.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="Command.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="SvcMessage.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="detours.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="drawing.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="interpreter.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="cvar.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="stringfinder.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="players.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="ModuleSecurity.h">
      <Filter>Header Files</Filter>
    </ClInclude>
  </ItemGroup>
  <ItemGroup>
    <ClCompile Include="AutoOffset.cpp">
      <Filter>Sources</Filter>
    </ClCompile>
    <ClCompile Include="Main.cpp">
      <Filter>Sources</Filter>
    </ClCompile>
    <ClCompile Include="ValveSDK\common\interface.cpp">
      <Filter>Header Files\ValveSDK\common</Filter>
    </ClCompile>
    <ClCompile Include="ValveSDK\misc\parsemsg.cpp">
      <Filter>Header Files\ValveSDK\misc</Filter>
    </ClCompile>
    <ClCompile Include="SetupHooks.cpp">
      <Filter>Sources</Filter>
    </ClCompile>
    <ClCompile Include="Client.cpp">
      <Filter>Sources</Filter>
    </ClCompile>
    <ClCompile Include="Command.cpp">
      <Filter>Sources</Filter>
    </ClCompile>
    <ClCompile Include="SvcMessage.cpp">
      <Filter>Sources</Filter>
    </ClCompile>
    <ClCompile Include="drawing.cpp">
      <Filter>Sources</Filter>
    </ClCompile>
    <ClCompile Include="usermsg.cpp">
      <Filter>Sources</Filter>
    </ClCompile>
    <ClCompile Include="interpreter.cpp">
      <Filter>Sources</Filter>
    </ClCompile>
    <ClCompile Include="cvar.cpp">
      <Filter>Sources</Filter>
    </ClCompile>
    <ClCompile Include="stringfinder.cpp">
      <Filter>Sources</Filter>
    </ClCompile>
    <ClCompile Include="players.cpp">
      <Filter>Sources</Filter>
    </ClCompile>
    <ClCompile Include="ModuleSecurity.cpp">
      <Filter>Sources</Filter>
    </ClCompile>
  </ItemGroup>
</Project>