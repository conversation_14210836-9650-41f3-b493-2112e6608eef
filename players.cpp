#include "players.h"

#pragma warning(disable:4996)
#pragma warning(disable:4800)
#pragma warning(disable:4002)
#pragma warning(disable:4244)

CMe::CMe(void)
{
	p_sInfo.eTeam = TEAM_NONE;

}

CMe::~CMe(){}

void CMe::GetInfo(void)
{
	p_sEnt = g_Engine.GetLocalPlayer();

	g_Engine.pfnGetScreenFade(&(p_sInfo.sScreenFade));

	g_Engine.pEventAPI->EV_LocalPlayerViewheight(p_sInfo.vViewOrg);
	VectorAdd(p_sEnt->origin, p_sInfo.vViewOrg, p_sInfo.vViewOrg);

	g_Engine.pfnGetPlayerInfo(p_sEnt->index, &sPlayerInfo);

	// External variable to force free-for-all mode
	extern bool g_bForceFreeForAll;

	// Check for free-for-all mode using multiple possible cvars
	bool isFreeForAll = g_bForceFreeForAll; // First check if manually forced

	if (!isFreeForAll) {
		// Check mp_gamemode (primary cvar)
		cvar_t* mp_gamemode = g_Engine.pfnGetCvarPointer("mp_gamemode");
		if (mp_gamemode && mp_gamemode->value == 0) {
			isFreeForAll = true;
		}

		// Check sv_gamemode (old incorrect cvar, but might be used by some servers)
		if (!isFreeForAll) {
			cvar_t* sv_gamemode = g_Engine.pfnGetCvarPointer("sv_gamemode");
			if (sv_gamemode && sv_gamemode->value == 0) {
				isFreeForAll = true;
			}
		}

		// Check mp_teamplay (some mods use this)
		if (!isFreeForAll) {
			cvar_t* mp_teamplay = g_Engine.pfnGetCvarPointer("mp_teamplay");
			if (mp_teamplay && mp_teamplay->value == 0) {
				isFreeForAll = true;
			}
		}
	}

	if (isFreeForAll)
	{
		// In free-for-all mode, everyone is on their own team
		p_sInfo.eTeam = TEAM_NONE;
	}
	else
	{
		// Team-based mode
		if ( g_Local.iTeam == 1 )
			p_sInfo.eTeam = TEAM_GOOD;
		else if ( g_Local.iTeam == 2 )
			p_sInfo.eTeam = TEAM_EVIL;
		else if ( g_Local.iTeam == 3 )
			p_sInfo.eTeam = TEAM_SPEC;
		else
			p_sInfo.eTeam = TEAM_NONE;
	}

	int iSequenceType = iSequenceInfoTable[p_sEnt->curstate.sequence];
	if (iSequenceType == 2 || iSequenceType == 5)
		p_sInfo.bIsReloading = true;
	else
		p_sInfo.bIsReloading = false;
}

cl_entity_t *CMe::Entity(void)
{
	return p_sEnt;
}

ME_INFO *CMe::Info(void)
{
	return &p_sInfo;
}

bool CMe::Visible(Vector *vInput)
{
	pmtrace_t *sTrace = g_Engine.PM_TraceLine(p_sInfo.vViewOrg, (*vInput), 0, 2, -1);
	return (sTrace->fraction >= 1.0f);
}

float CMe::Distance(Vector *vInput)
{
	return (float)(VectorDistance(p_sInfo.vViewOrg, (*vInput)) / 22.0f);
}

bool CMe::IsFlashed(void)
{
	return (!(!(p_sInfo.sScreenFade.fader == 255 && p_sInfo.sScreenFade.fadeg == 255 && p_sInfo.sScreenFade.fadeb == 255) ||
		p_sInfo.sScreenFade.fadealpha <= 150));
}

void CMe::CalcViewAngles(Vector *vOrigin, Vector *vOutput)
{
	Vector vDelta;
	VectorSubtract(&(p_sInfo.vViewOrg), vOrigin, &vDelta);

	// vOut->x
	float adjacent = sqrt(POW(vDelta.x) + POW(vDelta.y));
	p_sInfo.vAimAngles.x = (float)RADTODEG(atan(vDelta.z / adjacent));

	// vOut->y
	if (vDelta.x >= 0)		// front
	{
		if (vDelta.y >= 0)	// right
			p_sInfo.vAimAngles.y = (float)RADTODEG(atan(fabs(vDelta.y / vDelta.x))) + 180.0f;
		else				// left
			p_sInfo.vAimAngles.y = (float)RADTODEG(atan(fabs(vDelta.x / vDelta.y))) + 90.0f;
	}
	else					// behind
	{
		if (vDelta.y >= 0)
			p_sInfo.vAimAngles.y = (float)RADTODEG(atan(fabs(vDelta.x / vDelta.y))) + 270.0f;
		else
			p_sInfo.vAimAngles.y = (float)RADTODEG(atan(fabs(vDelta.y / vDelta.x)));
	}

	// vOut->z
	p_sInfo.vAimAngles.z = 0.0f;

	if (vOutput != NULL)
		VectorCopy(&(p_sInfo.vAimAngles), vOutput);
}

CPlayer::CPlayer(void){}
CPlayer::~CPlayer(){}

void CPlayer::GetInfo(int nID)
{
	p_sEnt				= g_Engine.GetEntityByIndex(nID);
	p_sInfo.bGotBone	= false;
	p_sInfo.bValid		= ValidateEntity();

	if (p_sInfo.bValid)
	{
		//SoundCopy(g_cPlayers.player(i)->cursound, g_cPlayers.player(i)->oldsound);

		VectorCopy(p_sEnt->origin, p_sInfo.sSound.vOrigin);	// todo p_sEnt -> p_pEnt
		p_sInfo.sSound.dwTime = GetTickCount();
		p_sInfo.sSound.bValid = true;

		int iSequenceType = iSequenceInfoTable[p_sEnt->curstate.sequence];
		if (iSequenceType == 2 || iSequenceType == 5)
			p_sInfo.bIsVulnerable = true;
		else
			p_sInfo.bIsVulnerable = false;

		g_Engine.pfnGetPlayerInfo(nID, &sPlayerInfo);

		// External variable to force free-for-all mode
		extern bool g_bForceFreeForAll;

		// Check for free-for-all mode using multiple possible cvars
		bool isFreeForAll = g_bForceFreeForAll; // First check if manually forced

		if (!isFreeForAll) {
			// Check mp_gamemode (primary cvar)
			cvar_t* mp_gamemode = g_Engine.pfnGetCvarPointer("mp_gamemode");
			if (mp_gamemode && mp_gamemode->value == 0) {
				isFreeForAll = true;
			}

			// Check sv_gamemode (old incorrect cvar, but might be used by some servers)
			if (!isFreeForAll) {
				cvar_t* sv_gamemode = g_Engine.pfnGetCvarPointer("sv_gamemode");
				if (sv_gamemode && sv_gamemode->value == 0) {
					isFreeForAll = true;
				}
			}

			// Check mp_teamplay (some mods use this)
			if (!isFreeForAll) {
				cvar_t* mp_teamplay = g_Engine.pfnGetCvarPointer("mp_teamplay");
				if (mp_teamplay && mp_teamplay->value == 0) {
					isFreeForAll = true;
				}
			}
		}

		// Log the free-for-all detection result
		static bool lastFreeForAllState = false;
		if (isFreeForAll != lastFreeForAllState) {
			g_Engine.Con_Printf("Free-for-all mode detection changed to: %s\n", isFreeForAll ? "TRUE" : "FALSE");
			lastFreeForAllState = isFreeForAll;
		}

		if (isFreeForAll)
		{
			// In free-for-all mode, everyone is on their own team
			p_sInfo.eTeam = TEAM_NONE;
		}
		else
		{
			// Team-based mode
			if ( g_Player[nID].iTeam == 1 )
				p_sInfo.eTeam = TEAM_GOOD;
			else if ( g_Player[nID].iTeam == 2 )
				p_sInfo.eTeam = TEAM_EVIL;
			else if ( g_Player[nID].iTeam == 3 )
				p_sInfo.eTeam = TEAM_SPEC;
			else
				p_sInfo.eTeam = TEAM_NONE;
		}
	}
}

cl_entity_t *CPlayer::Entity(void)
{
	return p_sEnt;
}

PPLAYER_INFO CPlayer::Info(void)
{
	return &p_sInfo;
}

bool CPlayer::ValidateEntity(void)
{
	// Check if entity pointer is valid
	if (!p_sEnt)
		return false;

	// Check if entity is not the local player
	if (p_sEnt->index == g_Local.iIndex)
		return false;

	// Check if entity is not hidden
	if (p_sEnt->curstate.effects & EF_NODRAW)
		return false;

	// Check if entity is a player
	if (!p_sEnt->player)
		return false;

	// Check if model is valid
	if (!p_sEnt->model || !p_sEnt->model->name || p_sEnt->model->name[0] == '\0')
		return false;

	// Check if entity has a valid message number (not outdated)
	if (p_sEnt->curstate.messagenum < g_Engine.GetLocalPlayer()->curstate.messagenum)
		return false;

	// Check if entity is alive (not in spectator mode)
	if (p_sEnt->curstate.movetype == 0 || p_sEnt->curstate.movetype == 6)
		return false;

	// All checks passed, entity is valid
	return true;
}

float CPlayer::Distance(Vector *vInput)
{
	return (float)(VectorDistance(p_sInfo.vOrigin, (*vInput)) / 22.0f);
}

float CPlayer::BoneDistance(Vector *vInput)
{
	return (float)(VectorDistance(p_sInfo.vAimBone, (*vInput)) / 22.0f);	// todo: use dyn vecs here
}

bool CPlayer::IsInFov(float fFov)
{
	if ((fFov == 0.0f)										||
		((p_sInfo.fScreen[0] <= g_Screen.iWidth/2 + fFov)	&&
		(p_sInfo.fScreen[0] >= g_Screen.iWidth/2 - fFov)	&&
		(p_sInfo.fScreen[1] <= g_Screen.iHeight/2 + fFov)	&&
		(p_sInfo.fScreen[1] >= g_Screen.iHeight/2 - fFov)))
		return true;

	return false;
}

Vector *CPlayer::Position(void)
{
	return &(p_sInfo.vOrigin);
}

Vector *CPlayer::BonePosition(void)
{
	return &(p_sInfo.vAimBone);
//	vOutput->x = (p_sInfo.sBones)[nBoneID][0][3];
//	vOutput->y = (p_sInfo.sBones)[nBoneID][1][3];
//	vOutput->z = (p_sInfo.sBones)[nBoneID][2][3];
}

bool CalcScreen(Vector *vInput, float *fOutput)
{
	// Validate input vector
	if (!vInput || isnan(vInput->x) || isnan(vInput->y) || isnan(vInput->z) ||
		fabs(vInput->x) > 10000.0f || fabs(vInput->y) > 10000.0f || fabs(vInput->z) > 10000.0f)
	{
		// Invalid input, set to center of screen
		fOutput[0] = g_Screen.iWidth / 2.0f;
		fOutput[1] = g_Screen.iHeight / 2.0f;
		return false;
	}

	// Get screen dimensions
	int iScreenCenterX = g_Screen.iWidth / 2;
	int iScreenCenterY = g_Screen.iHeight / 2;

	// Convert world position to screen position
	int iRasterizer = g_Engine.pTriAPI->WorldToScreen((*vInput), fOutput);

	// Check if WorldToScreen returned valid values
	if (isnan(fOutput[0]) || isnan(fOutput[1]))
	{
		fOutput[0] = g_Screen.iWidth / 2.0f;
		fOutput[1] = g_Screen.iHeight / 2.0f;
		return false;
	}

	// Calculate screen coordinates
	fOutput[0] = fOutput[0] * iScreenCenterX + iScreenCenterX;
	fOutput[1] = -fOutput[1] * iScreenCenterY + iScreenCenterY;

	// Clamp values to prevent extreme off-screen coordinates
	if (fOutput[0] < -g_Screen.iWidth) fOutput[0] = -g_Screen.iWidth;
	if (fOutput[0] > g_Screen.iWidth * 2) fOutput[0] = g_Screen.iWidth * 2;
	if (fOutput[1] < -g_Screen.iHeight) fOutput[1] = -g_Screen.iHeight;
	if (fOutput[1] > g_Screen.iHeight * 2) fOutput[1] = g_Screen.iHeight * 2;

	// Return true if the player is on screen, false otherwise
	return (fOutput[0] >= 0 && fOutput[0] <= g_Screen.iWidth &&
		    fOutput[1] >= 0 && fOutput[1] <= g_Screen.iHeight &&
		    !iRasterizer);
}
void VectorTransform(float *in1, float in2[3][4], float *out)
{
	out[0] = DotProduct(in1, in2[0]) + in2[0][3];
	out[1] = DotProduct(in1, in2[1]) + in2[1][3];
	out[2] = DotProduct(in1, in2[2]) + in2[2][3];
}

bool CPlayer::ScreenPosition(float *fOutput)
{
	p_sInfo.bIsScreen = CalcScreen(Position(), p_sInfo.fScreen);
	if (fOutput != NULL) { fOutput[0] = p_sInfo.fScreen[0]; fOutput[1] = p_sInfo.fScreen[1]; }
	return p_sInfo.bIsScreen;
}

bool CPlayer::BoneScreenPosition(float *fOutput)
{
	p_sInfo.bIsBoneScreen = CalcScreen(BonePosition(), p_sInfo.fBoneScreen);
	if (fOutput != NULL) { fOutput[0] = p_sInfo.fBoneScreen[0]; fOutput[1] = p_sInfo.fBoneScreen[1]; }
	return p_sInfo.bIsBoneScreen;

}

void CPlayer::GetBoneInformation(VECTOR_MODE eMode, int nNum)
{
	// Validate entity before proceeding
	if (!p_sEnt || !p_sInfo.bValid)
	{
		// If entity is invalid, set bone position to origin to avoid invalid positions
		VectorCopy(p_sEnt ? p_sEnt->origin : Vector(0, 0, 0), p_sInfo.vAimBone);
		return;
	}

	model_s	*pModel = nullptr;
	studiohdr_t	*pStudioHeader = nullptr;
	mstudiobbox_t *pStudioBox = nullptr;
	PTRANSFORM_MATRIX pTransformMatrix = (PTRANSFORM_MATRIX)g_Studio.StudioGetBoneTransform();

	// Check if transform matrix is valid
	if (!pTransformMatrix)
	{
		// If transform matrix is invalid, set bone position to entity origin
		VectorCopy(p_sEnt->origin, p_sInfo.vAimBone);
		return;
	}

	try
	{
		// switch me!
		if (eMode == VM_BONE)
		{
			// Validate bone index
			if (nNum < 0 || nNum >= 128) // Assuming max bones is 128
			{
				VectorCopy(p_sEnt->origin, p_sInfo.vAimBone);
				return;
			}

			p_sInfo.vAimBone.x = (*pTransformMatrix)[nNum][0][3];
			p_sInfo.vAimBone.y = (*pTransformMatrix)[nNum][1][3];
			p_sInfo.vAimBone.z = (*pTransformMatrix)[nNum][2][3];

			// Validate bone position (check for NaN or extreme values)
			if (isnan(p_sInfo.vAimBone.x) || isnan(p_sInfo.vAimBone.y) || isnan(p_sInfo.vAimBone.z) ||
				fabs(p_sInfo.vAimBone.x) > 10000.0f || fabs(p_sInfo.vAimBone.y) > 10000.0f || fabs(p_sInfo.vAimBone.z) > 10000.0f)
			{
				// Invalid bone position, use entity origin instead
				VectorCopy(p_sEnt->origin, p_sInfo.vAimBone);
			}
		}
		else if (eMode == VM_HITBOX)
		{
			if (p_sInfo.bGotBone)
				return;

			pModel = g_Studio.SetupPlayerModel(p_sEnt->index);
			if (!pModel)
			{
				VectorCopy(p_sEnt->origin, p_sInfo.vAimBone);
				return;
			}

			pStudioHeader = (studiohdr_t *)g_Studio.Mod_Extradata(pModel);
			if (!pStudioHeader)
			{
				VectorCopy(p_sEnt->origin, p_sInfo.vAimBone);
				return;
			}

			// Validate hitbox index
			if (nNum < 0 || nNum >= pStudioHeader->numhitboxes)
			{
				VectorCopy(p_sEnt->origin, p_sInfo.vAimBone);
				return;
			}

			pStudioBox = (mstudiobbox_t *)((PBYTE)pStudioHeader + pStudioHeader->hitboxindex);

			Vector vMin, vMax;
			VectorTransform(pStudioBox[nNum].bbmin, (*pTransformMatrix)[pStudioBox[nNum].bone], vMin);
			VectorTransform(pStudioBox[nNum].bbmax, (*pTransformMatrix)[pStudioBox[nNum].bone], vMax);

			// Validate hitbox position
			if (isnan(vMin.x) || isnan(vMin.y) || isnan(vMin.z) ||
				isnan(vMax.x) || isnan(vMax.y) || isnan(vMax.z) ||
				fabs(vMin.x) > 10000.0f || fabs(vMin.y) > 10000.0f || fabs(vMin.z) > 10000.0f ||
				fabs(vMax.x) > 10000.0f || fabs(vMax.y) > 10000.0f || fabs(vMax.z) > 10000.0f)
			{
				VectorCopy(p_sEnt->origin, p_sInfo.vAimBone);
			}
			else
			{
				p_sInfo.vAimBone = (vMin + vMax) * 0.5f;
			}

			p_sInfo.bGotBone = true;
		}
	}
	catch (...)
	{
		// If any exception occurs, use entity origin as fallback
		VectorCopy(p_sEnt->origin, p_sInfo.vAimBone);
	}
}

CPlayers::CPlayers(void)
{
	p_cMe			= new CMe;
	p_cPlayers		= new CPlayer[MAX_VPLAYERS];
}

CPlayers::~CPlayers()
{
	delete[]	p_cPlayers;
	delete		p_cMe;
}

int CPlayers::Size(void)
{
	return MAX_VPLAYERS;
}

CMe *CPlayers::Me(void)
{
	return p_cMe;
}

CPlayer *CPlayers::Player(int i)
{
	if (i >= 0 && i < MAX_VPLAYERS)
		return &p_cPlayers[i];
	return &p_cPlayers[0];
}